# Design Patterns & Examples

## Overview

This document showcases real examples from the Marketing Therapy Journey codebase, demonstrating how our neo-brutalist design system and component patterns are implemented in practice.

## Landing Page Patterns

### 1. Hero Section Pattern

The Hero component demonstrates our signature animated entrance and floating elements:

<augment_code_snippet path="src/components/Hero.tsx" mode="EXCERPT">
````tsx
<section className="pt-32 pb-20 md:pt-40 md:pb-28 overflow-hidden">
  <div className="container mx-auto px-4 relative">
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <div className="relative z-10">
        <div className="animate-slide-in [animation-delay:0.1s] opacity-0">
          <span className="inline-block px-4 py-2 neo-border bg-accent text-white font-bold mb-4 -rotate-1">
            Marketing Experts
          </span>
        </div>
        
        <h1 className="text-4xl md:text-6xl font-bold leading-tight mb-6 animate-slide-in [animation-delay:0.2s] opacity-0">
          Marketing Therapy <span className="relative inline-block">
            Sessions
            <svg className="absolute -bottom-2 left-0 w-full h-3 text-primary" viewBox="0 0 100 8" preserveAspectRatio="none">
              <path d="M0,5 Q40,0 50,5 Q60,10 100,5" stroke="currentColor" strokeWidth="4" fill="none" />
            </svg>
          </span>
        </h1>
````
</augment_code_snippet>

**Key Patterns:**
- **Staggered Animations**: Each element has increasing animation delays
- **Rotated Elements**: `-rotate-1` for playful positioning
- **SVG Underlines**: Custom SVG paths for decorative elements
- **Responsive Typography**: `text-4xl md:text-6xl` scaling

### 2. Benefits Grid Pattern

The Benefits component shows our card grid system with dynamic colors:

<augment_code_snippet path="src/components/Benefits.tsx" mode="EXCERPT">
````tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
  {benefits.map((benefit, index) => (
    <div 
      key={index}
      className="neo-card p-6 bg-white hover:-translate-y-2 cursor-pointer"
      style={{ 
        backgroundColor: index % 4 === 0 ? '#FFFFFF' : 
                         index % 4 === 1 ? '#FFD700' : 
                         index % 4 === 2 ? '#00A3FF' : 
                         '#FF3366',
        transform: `rotate(${index % 2 === 0 ? '1' : '-1'}deg)`
      }}
    >
      <div className="mb-4 text-black">{benefit.icon}</div>
      <h3 className="text-xl font-bold mb-3">{benefit.title}</h3>
      <p>{benefit.description}</p>
    </div>
  ))}
</div>
````
</augment_code_snippet>

**Key Patterns:**
- **Dynamic Color Assignment**: Modulo-based color cycling
- **Alternating Rotations**: Even/odd index rotation logic
- **Hover Effects**: `hover:-translate-y-2` for lift animation
- **Responsive Grid**: Mobile-first grid system

### 3. Process Timeline Pattern

The Process component demonstrates connected step visualization:

<augment_code_snippet path="src/components/Process.tsx" mode="EXCERPT">
````tsx
<div className="relative">
  {/* Connection line */}
  <div className="hidden md:block absolute top-1/2 left-0 right-0 h-1 bg-black transform -translate-y-1/2 z-0"></div>
  
  <div className="grid grid-cols-1 md:grid-cols-3 gap-8 relative z-10">
    {steps.map((step, index) => (
      <div key={index} className="flex flex-col items-center text-center">
        <div 
          className="w-20 h-20 flex items-center justify-center neo-border bg-white text-2xl font-bold mb-6"
          style={{ transform: `rotate(${index % 2 === 0 ? '3' : '-3'}deg)` }}
        >
          {step.number}
        </div>
        <h3 className="text-xl font-bold mb-3">{step.title}</h3>
        <p>{step.description}</p>
      </div>
    ))}
  </div>
</div>
````
</augment_code_snippet>

**Key Patterns:**
- **Layered Layout**: Absolute positioning with z-index management
- **Connected Elements**: Visual connection line between steps
- **Numbered Circles**: Rotated number badges
- **Responsive Hiding**: `hidden md:block` for connection line

## Form Component Patterns

### 1. Multi-Step Form Structure

The Booking page demonstrates our wizard pattern:

<augment_code_snippet path="src/pages/Booking.tsx" mode="EXCERPT">
````tsx
const StepSelector = () => {
  const { currentStep } = useFormContext();

  switch (currentStep) {
    case 1:
      return <StepOne />;
    case 2:
      return <StepTwo />;
    case 3:
      return <StepThree />;
    case 4:
      return <StepFour />;
    default:
      return <StepOne />;
  }
};
````
</augment_code_snippet>

**Key Patterns:**
- **Step Selector**: Switch-based component rendering
- **Context Integration**: Shared state across all steps
- **Progress Tracking**: Visual progress indicator

### 2. Form Field Validation Pattern

StepOne shows our validation and error handling approach:

<augment_code_snippet path="src/components/booking/StepOne.tsx" mode="EXCERPT">
````tsx
<div>
  <Label htmlFor="firstName" className="block mb-1 font-medium">
    First Name
  </Label>
  <Input 
    type="text"
    id="firstName"
    name="firstName"
    value={formData.firstName}
    onChange={handleChange}
    className={`neo-input w-full ${errors.firstName ? 'border-red-500' : ''}`}
  />
  {errors.firstName && (
    <p className="mt-1 text-red-500 text-sm">{errors.firstName}</p>
  )}
</div>
````
</augment_code_snippet>

**Key Patterns:**
- **Conditional Styling**: Error state border changes
- **Error Display**: Conditional error message rendering
- **Consistent Spacing**: Standardized margin/padding

### 3. Radio Group Pattern

StepTwo demonstrates our radio button styling:

<augment_code_snippet path="src/components/booking/StepTwo.tsx" mode="EXCERPT">
````tsx
<RadioGroup
  value={formData.businessType}
  onValueChange={(value) => handleRadioChange('businessType', value)}
  className="space-y-2"
>
  {BUSINESS_TYPES.map(type => (
    <div key={type.value} className="flex items-center space-x-2 neo-border p-3">
      <RadioGroupItem value={type.value} id={`business_type_${type.value}`} />
      <Label htmlFor={`business_type_${type.value}`}>{type.label}</Label>
    </div>
  ))}
</RadioGroup>
````
</augment_code_snippet>

**Key Patterns:**
- **Neo-Border Containers**: Each option wrapped in styled container
- **Consistent Spacing**: `space-y-2` and `space-x-2` for gaps
- **Proper IDs**: Unique IDs for accessibility

## Context & State Patterns

### 1. Form Context Implementation

The FormContext shows our state management approach:

<augment_code_snippet path="src/contexts/FormContext.tsx" mode="EXCERPT">
````tsx
interface FormContextType {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
  currentStep: number;
  setCurrentStep: (step: number) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  validateCurrentStep: () => boolean;
  errors: Record<string, string>;
  setErrors: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  leadId: string | null;
}
````
</augment_code_snippet>

**Key Patterns:**
- **Comprehensive Interface**: All form operations in one context
- **Type Safety**: Full TypeScript coverage
- **Validation Integration**: Built-in validation methods

### 2. Database Integration Pattern

The FormContext demonstrates Neon database integration:

<augment_code_snippet path="src/contexts/FormContext.tsx" mode="EXCERPT">
````tsx
const saveLeadData = async (data: Partial<FormData>, id?: string) => {
  try {
    if (id) {
      // Update existing lead
      await sql`
        UPDATE leads 
        SET first_name = ${data.firstName}, 
            last_name = ${data.lastName},
            email = ${data.email},
            updated_at = NOW()
        WHERE id = ${id}
      `;
    } else {
      // Create new lead
      const result = await sql`
        INSERT INTO leads (first_name, last_name, email, created_at)
        VALUES (${data.firstName}, ${data.lastName}, ${data.email}, NOW())
        RETURNING id
      `;
      return result[0].id;
    }
  } catch (error) {
    console.error('Database error:', error);
    throw error;
  }
};
````
</augment_code_snippet>

**Key Patterns:**
- **Upsert Logic**: Create or update based on ID presence
- **Error Handling**: Try-catch with proper error logging
- **SQL Template Literals**: Parameterized queries for security

## Animation & Interaction Patterns

### 1. CSS Animation Classes

Our custom animation system in index.css:

<augment_code_snippet path="src/index.css" mode="EXCERPT">
````css
.neo-border {
  @apply border-[3px] border-black;
}

.neo-shadow {
  @apply shadow-[4px_4px_0px_0px_rgba(0,0,0,1)];
}

.neo-card {
  @apply neo-border neo-shadow bg-white transition-all duration-300;
}

.neo-button {
  @apply neo-border neo-shadow px-6 py-3 font-bold transition-all duration-200 
         active:translate-x-[2px] active:translate-y-[2px] 
         active:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)];
}
````
</augment_code_snippet>

**Key Patterns:**
- **Utility Classes**: Reusable neo-brutalist components
- **Active States**: Button press animations
- **Consistent Shadows**: Standardized shadow offsets

### 2. Floating Elements Pattern

Hero section floating shapes:

<augment_code_snippet path="src/components/Hero.tsx" mode="EXCERPT">
````tsx
<div className="relative hidden lg:block">
  <div className="absolute -top-10 -left-10 w-40 h-40 bg-yellow neo-border rotate-12 animate-float"></div>
  <div className="absolute top-20 -right-5 w-24 h-24 bg-pink neo-border -rotate-6 animate-float [animation-delay:1s]"></div>
  <div className="absolute -bottom-10 left-20 w-32 h-32 bg-blue neo-border rotate-3 animate-float [animation-delay:2s]"></div>
</div>
````
</augment_code_snippet>

**Key Patterns:**
- **Absolute Positioning**: Precise placement of decorative elements
- **Staggered Delays**: Different animation delays for organic feel
- **Responsive Hiding**: `hidden lg:block` for desktop-only elements

## Responsive Design Patterns

### 1. Mobile-First Grid System

Benefits component responsive grid:

<augment_code_snippet path="src/components/Benefits.tsx" mode="EXCERPT">
````tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
  {benefits.map((benefit, index) => (
    <div className="neo-card p-6 bg-white hover:-translate-y-2 cursor-pointer">
      {/* Card content */}
    </div>
  ))}
</div>
````
</augment_code_snippet>

**Key Patterns:**
- **Progressive Enhancement**: 1 → 2 → 4 columns
- **Consistent Gaps**: Single gap value across breakpoints
- **Hover States**: Desktop-focused interactions

### 2. Responsive Typography

Header component responsive text sizing:

<augment_code_snippet path="src/components/Header.tsx" mode="EXCERPT">
````tsx
<a href="#" className="font-bold text-xl md:text-2xl tracking-tight hover:text-primary transition-colors">
  PleaseHelpMe<span className="text-accent">.Marketing</span>
</a>
````
</augment_code_snippet>

**Key Patterns:**
- **Incremental Scaling**: `text-xl md:text-2xl`
- **Brand Highlighting**: Accent color for domain extension
- **Smooth Transitions**: `transition-colors` for hover states

## Error Handling Patterns

### 1. Toast Notification Pattern

StepOne website analysis error handling:

<augment_code_snippet path="src/components/booking/StepOne.tsx" mode="EXCERPT">
````tsx
try {
  toast({
    title: "Fetching website info",
    description: "Getting preview information...",
    duration: 3000,
  });
  
  const metadata = await fetchWebsiteMetadata(url);
  setWebsiteMetadata(metadata);
  
  if (metadata.title) {
    toast({
      title: "Website analyzed successfully",
      description: "We found some great info about your site!",
    });
  }
} catch (error) {
  toast({
    title: "Couldn't analyze website",
    description: "No worries, you can still continue with the booking.",
    variant: "destructive"
  });
}
````
</augment_code_snippet>

**Key Patterns:**
- **Progressive Feedback**: Loading → Success/Error states
- **User-Friendly Messages**: Clear, non-technical language
- **Graceful Degradation**: Allow continuation despite errors

### 2. Form Validation Pattern

FormContext validation implementation:

<augment_code_snippet path="src/contexts/FormContext.tsx" mode="EXCERPT">
````tsx
const validateStep1 = (): boolean => {
  const newErrors: Record<string, string> = {};
  
  if (!formData.firstName.trim()) {
    newErrors.firstName = 'First name is required';
  }
  
  if (!formData.email.trim()) {
    newErrors.email = 'Email is required';
  } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
    newErrors.email = 'Please enter a valid email address';
  }
  
  setErrors(newErrors);
  return Object.keys(newErrors).length === 0;
};
````
</augment_code_snippet>

**Key Patterns:**
- **Comprehensive Validation**: Multiple validation rules per field
- **Clear Error Messages**: Specific, actionable feedback
- **Boolean Return**: Simple success/failure indication

## Performance Patterns

### 1. Conditional Rendering

StepOne conditional field display:

<augment_code_snippet path="src/components/booking/StepOne.tsx" mode="EXCERPT">
````tsx
{showFields && (
  <div className={`space-y-4 ${isScrapingWebsite ? 'opacity-50 pointer-events-none' : ''}`}>
    <div>
      <Label htmlFor="companyName" className="block mb-1 font-medium flex items-center">
        Company Name
        {analyzedFields.companyName && (
          <span className="ml-2 text-xs bg-primary/20 text-primary px-2 py-0.5 rounded-full">
            Auto-filled
          </span>
        )}
      </Label>
      {/* Input field */}
    </div>
  </div>
)}
````
</augment_code_snippet>

**Key Patterns:**
- **Progressive Disclosure**: Show fields only when needed
- **Loading States**: Visual feedback during async operations
- **Auto-fill Indicators**: Clear labeling of automated fields

### 2. Optimized Imports

Component import patterns:

<augment_code_snippet path="src/components/booking/StepOne.tsx" mode="EXCERPT">
````tsx
import React, { useState, useEffect } from 'react';
import { useFormContext } from '../../contexts/FormContext';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { useToast } from '../../hooks/use-toast';
import { Loader2 } from 'lucide-react';
````
</augment_code_snippet>

**Key Patterns:**
- **Specific Imports**: Import only needed components
- **Grouped Imports**: React, context, UI, hooks, icons
- **Relative Paths**: Consistent path structure

## Integration Patterns

### 1. Service Integration

Website metadata service integration:

<augment_code_snippet path="src/components/booking/StepOne.tsx" mode="EXCERPT">
````tsx
import { 
  fetchWebsiteMetadata, 
  normalizeUrl, 
  analyzeWebsiteWithAI 
} from '../../services/websiteMetadataService';

const handleWebsiteAnalysis = async (url: string) => {
  try {
    const normalizedUrl = normalizeUrl(url);
    const metadata = await fetchWebsiteMetadata(normalizedUrl);
    
    if (metadata.title) {
      const analysis = await analyzeWebsiteWithAI(metadata);
      // Apply analysis results
    }
  } catch (error) {
    // Handle error
  }
};
````
</augment_code_snippet>

**Key Patterns:**
- **Service Abstraction**: Separate service layer for external APIs
- **URL Normalization**: Consistent URL formatting
- **Chained Operations**: Sequential API calls with error handling

This comprehensive documentation provides real-world examples of how our design system and component patterns are implemented throughout the Marketing Therapy Journey application.
