export interface NavigationLink {
  href: string;
  label: string;
  external?: boolean;
}

export const navigationLinks: NavigationLink[] = [
  { href: '/', label: 'Home' },
  { href: '/services', label: 'Services' },
  { href: '/#benefits', label: 'Benefits' },
  { href: '/#process', label: 'Process' },
  { href: '/#testimonials', label: 'Testimonials' },
  { href: '/#pricing', label: 'Pricing' },
];

export const ctaButton = {
  href: '/booking',
  label: 'Book Your Session',
};
