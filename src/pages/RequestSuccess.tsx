import React from 'react';
import { CheckCircle, Calendar, Mail, ArrowRight, Clock } from 'lucide-react';
import { Link } from 'react-router-dom';
import Header from '../components/Header';
import Footer from '../components/Footer';

const RequestSuccess = () => {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="pt-32 pb-20">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            {/* Success Icon */}
            <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-8">
              <CheckCircle className="h-10 w-10 text-white" />
            </div>

            {/* Success Message */}
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Request Submitted!
            </h1>
            <p className="text-lg text-gray-600 mb-8">
              Thank you for your consultation request! We've received your information and will review it carefully.
            </p>

            {/* Next Steps */}
            <div className="neo-card p-8 bg-primary text-left mb-8">
              <h2 className="text-xl font-bold mb-4">What Happens Next?</h2>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-black text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                    1
                  </div>
                  <div>
                    <h3 className="font-bold flex items-center">
                      <Clock className="h-4 w-4 mr-2" />
                      Review (Within 24 Hours)
                    </h3>
                    <p className="text-sm">Our team will carefully review your request and project requirements.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-black text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                    2
                  </div>
                  <div>
                    <h3 className="font-bold flex items-center">
                      <Mail className="h-4 w-4 mr-2" />
                      Custom Proposal
                    </h3>
                    <p className="text-sm">We'll send you a detailed proposal with pricing, timeline, and next steps.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-black text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                    3
                  </div>
                  <div>
                    <h3 className="font-bold flex items-center">
                      <Calendar className="h-4 w-4 mr-2" />
                      Discovery Call
                    </h3>
                    <p className="text-sm">If you're interested, we'll schedule a call to discuss details and finalize the project.</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="neo-card p-6 bg-white mb-8 text-left">
              <h3 className="text-lg font-bold mb-4">Need to Add Something?</h3>
              <p className="text-gray-600 mb-4">
                If you forgot to mention something important or have additional questions, feel free to reach out directly.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <a 
                  href="mailto:<EMAIL>" 
                  className="neo-button bg-secondary text-white hover:bg-secondary/90 px-6 py-3 text-center"
                >
                  <Mail className="h-4 w-4 mr-2 inline" />
                  Email Us
                </a>
                <Link 
                  to="/booking"
                  className="neo-button bg-white hover:bg-gray-50 text-black px-6 py-3 text-center"
                >
                  <Calendar className="h-4 w-4 mr-2 inline" />
                  Schedule a Call
                </Link>
              </div>
            </div>

            {/* Response Time Guarantee */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
              <h3 className="text-lg font-bold text-green-800 mb-2">24-Hour Response Guarantee</h3>
              <p className="text-green-700">
                We guarantee to respond to your request within 24 hours during business days. 
                Most requests receive a response within 4-6 hours.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                to="/services"
                className="neo-button bg-primary hover:bg-primary/90 text-black px-8 py-3"
              >
                Browse Other Services
              </Link>
              <Link 
                to="/"
                className="neo-button bg-white hover:bg-gray-50 text-black px-8 py-3"
              >
                Back to Home
              </Link>
            </div>

            {/* Additional Info */}
            <div className="mt-12 text-center">
              <p className="text-gray-600 mb-4">
                Questions about your request?
              </p>
              <a 
                href="mailto:<EMAIL>" 
                className="text-blue-600 hover:underline font-medium"
              >
                <EMAIL>
              </a>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default RequestSuccess;
