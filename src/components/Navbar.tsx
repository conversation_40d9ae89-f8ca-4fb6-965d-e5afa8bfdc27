import React from 'react';
import { navigationLinks, ctaButton } from '../data/navigationLinks';

interface NavbarProps {
  isMobile?: boolean;
  onLinkClick?: () => void;
  className?: string;
}

const Navbar: React.FC<NavbarProps> = ({ 
  isMobile = false, 
  onLinkClick,
  className = '' 
}) => {
  const handleLinkClick = () => {
    if (onLinkClick) {
      onLinkClick();
    }
  };

  if (isMobile) {
    return (
      <div className={`md:hidden ${className}`}>
        <ul className="space-y-4">
          {navigationLinks.map((link) => (
            <li key={link.href}>
              <a
                href={link.href}
                className="block py-2 hover:text-primary font-medium transition-colors"
                onClick={handleLinkClick}
              >
                {link.label}
              </a>
            </li>
          ))}
          <li>
            <a
              href={ctaButton.href}
              className="block py-2 text-center neo-button bg-primary hover:bg-primary/90"
              onClick={handleLinkClick}
            >
              {ctaButton.label}
            </a>
          </li>
        </ul>
      </div>
    );
  }

  return (
    <nav className={`hidden md:block ${className}`}>
      <ul className="flex space-x-8">
        {navigationLinks.map((link) => (
          <li key={link.href}>
            <a
              href={link.href}
              className="hover:text-primary font-medium transition-colors"
            >
              {link.label}
            </a>
          </li>
        ))}
      </ul>
    </nav>
  );
};

export default Navbar;
