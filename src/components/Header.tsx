import React, { useState, useEffect } from 'react';
import { Menu, X } from 'lucide-react';
import { useLocation } from 'react-router-dom';
import InteractiveLogo from './InteractiveLogo';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const location = useLocation();
  const isServicesPage = location.pathname === '/services';

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 ${
        isScrolled
          ? 'h-16 bg-white neo-border border-t-0 border-l-0 border-r-0'
          : 'h-20 bg-transparent'
      }`}
    >
      <div className="container mx-auto px-4 h-full flex justify-between items-center">
        <div className="brand flex-shrink-0">
          <InteractiveLogo isScrolled={isScrolled} />
        </div>
        
        <nav className="hidden md:block">
          <ul className="flex space-x-8">
            <li><a href="/" className="hover:text-primary font-medium transition-colors">Home</a></li>
            <li><a href="/services" className="hover:text-primary font-medium transition-colors">Services</a></li>
            {!isServicesPage && (
              <>
                <li><a href="#benefits" className="hover:text-primary font-medium transition-colors">Benefits</a></li>
                <li><a href="#process" className="hover:text-primary font-medium transition-colors">Process</a></li>
                <li><a href="#testimonials" className="hover:text-primary font-medium transition-colors">Testimonials</a></li>
              </>
            )}
            <li><a href={isServicesPage ? "#pricing" : "#pricing"} className="hover:text-primary font-medium transition-colors">Pricing</a></li>
          </ul>
        </nav>
        
        <div className="hidden md:block">
          <a href={isServicesPage ? "#pricing" : "/booking"} className="neo-button bg-primary hover:bg-primary/90">
            {isServicesPage ? "Get Started" : "Book Your Session"}
          </a>
        </div>
        
        <button 
          className="block md:hidden text-black"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        >
          {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>
      
      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="md:hidden absolute top-full left-0 right-0 bg-white neo-border border-t-0 border-l-0 border-r-0 py-4 animate-fade-in">
          <div className="container mx-auto px-4">
            <ul className="space-y-4">
              <li><a href="/" className="block py-2 hover:text-primary font-medium transition-colors" onClick={() => setMobileMenuOpen(false)}>Home</a></li>
              <li><a href="/services" className="block py-2 hover:text-primary font-medium transition-colors" onClick={() => setMobileMenuOpen(false)}>Services</a></li>
              {!isServicesPage && (
                <>
                  <li><a href="#benefits" className="block py-2 hover:text-primary font-medium transition-colors" onClick={() => setMobileMenuOpen(false)}>Benefits</a></li>
                  <li><a href="#process" className="block py-2 hover:text-primary font-medium transition-colors" onClick={() => setMobileMenuOpen(false)}>Process</a></li>
                  <li><a href="#testimonials" className="block py-2 hover:text-primary font-medium transition-colors" onClick={() => setMobileMenuOpen(false)}>Testimonials</a></li>
                </>
              )}
              <li><a href={isServicesPage ? "#pricing" : "#pricing"} className="block py-2 hover:text-primary font-medium transition-colors" onClick={() => setMobileMenuOpen(false)}>Pricing</a></li>
              <li>
                <a
                  href={isServicesPage ? "#pricing" : "/booking"}
                  className="block py-2 text-center neo-button bg-primary hover:bg-primary/90"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {isServicesPage ? "Get Started" : "Book Your Session"}
                </a>
              </li>
            </ul>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;