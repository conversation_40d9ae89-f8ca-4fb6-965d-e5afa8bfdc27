import React, { useState, useEffect } from 'react';
import { ChevronDown, User, Calendar, MessageCircle, Settings, LogOut, Home, Briefcase, Mail, ExternalLink } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from './ui/dropdown-menu';

interface InteractiveLogoProps {
  className?: string;
  variant?: 'default' | 'compact';
  showStatusText?: boolean;
  isScrolled?: boolean;
}

type OnlineStatus = 'online' | 'away' | 'offline';

const InteractiveLogo: React.FC<InteractiveLogoProps> = ({
  className = '',
  variant = 'default',
  showStatusText = true,
  isScrolled = false,
}) => {
  const [onlineStatus, setOnlineStatus] = useState<OnlineStatus>('offline');
  const [isOpen, setIsOpen] = useState(false);

  // Calculate online status based on current time
  useEffect(() => {
    const updateOnlineStatus = () => {
      const now = new Date();
      const hour = now.getHours();

      if (hour >= 8 && hour < 17) {
        setOnlineStatus('online');
      } else if ((hour >= 6 && hour < 8) || (hour >= 17 && hour < 22)) {
        setOnlineStatus('away');
      } else {
        setOnlineStatus('offline');
      }
    };

    // Update immediately
    updateOnlineStatus();

    // Update every minute
    const interval = setInterval(updateOnlineStatus, 60000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: OnlineStatus) => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'away':
        return 'bg-yellow-500';
      case 'offline':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusStyle = (status: OnlineStatus) => {
    switch (status) {
      case 'online':
        return { backgroundColor: '#10b981' }; // green-500
      case 'away':
        return { backgroundColor: '#eab308' }; // yellow-500
      case 'offline':
        return { backgroundColor: '#ef4444' }; // red-500
      default:
        return { backgroundColor: '#6b7280' }; // gray-500
    }
  };

  const getStatusText = (status: OnlineStatus) => {
    switch (status) {
      case 'online':
        return 'Available for consultation';
      case 'away':
        return 'Limited availability';
      case 'offline':
        return 'Currently offline';
      default:
        return 'Status unknown';
    }
  };

  return (
    <div className="relative">
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen} modal={false}>
        <DropdownMenuTrigger asChild>
          <button
            className={`group relative flex items-center gap-3 bg-white border-4 border-black rounded-full pl-1 pr-6 py-1 transition-all duration-300 ease-out hover:shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] focus:outline-none ${
              isScrolled ? 'scale-90' : ''
            } ${className}`}
          >
          {/* Avatar with Status Indicator */}
          <div className="relative">
            <div className={`rounded-full overflow-hidden border-2 border-black bg-gray-100 transition-all duration-300 ${
                isScrolled ? 'w-10 h-10' : 'w-12 h-12'
              }`}>
              <img
                src="/me.png"
                alt="Asger Profile"
                className="w-full h-full object-cover"
              />
            </div>

            {/* Status Indicator */}
            <div
              className={`absolute -bottom-1 -left-1 w-4 h-4 rounded-full border-2 border-white transition-colors duration-300 ${getStatusColor(onlineStatus)}`}
              title={getStatusText(onlineStatus)}
              style={{
                boxShadow: '0 0 0 2px black',
                ...getStatusStyle(onlineStatus)
              }}
            />
          </div>

          {/* Logo Text */}
          <div className="flex items-center gap-2">
            <span className={`font-bold tracking-tight text-black transition-all duration-300 ${
                isScrolled ? 'text-base' : 'text-lg'
              }`}>
              Asger.me
            </span>
            <ChevronDown
              className={`w-4 h-4 text-black transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
            />
          </div>
        </button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent
        className="w-64 neo-border neo-shadow bg-white p-0 rounded-lg overflow-hidden [&[data-state=open]]:animate-none [&[data-state=closed]]:animate-none"
        sideOffset={8}
        align="start"
        avoidCollisions={false}
        side="bottom"
      >
        {/* Header Section */}
        <div className="p-4 bg-primary border-b-2 border-black relative overflow-hidden">
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
          <div className="absolute bottom-0 left-0 w-16 h-16 bg-black/10 rounded-full translate-y-8 -translate-x-8"></div>

          <div className="flex items-center space-x-3 relative z-10">
            <div className="relative z-10">
              <div className="w-12 h-12 rounded-full overflow-hidden neo-border bg-gray-100">
                <img
                  src="/me.png"
                  alt="Asger Profile"
                  className="w-full h-full object-cover"
                />
              </div>
              <div
                className={`absolute -bottom-0.5 -right-0.5 w-4 h-4 rounded-full border-2 border-white transition-all duration-300 z-20 ${getStatusColor(onlineStatus)}`}
                style={{
                  boxShadow: '0 0 0 2px black',
                  ...getStatusStyle(onlineStatus)
                }}
              />
            </div>
            <div>
              <h3 className="font-bold text-black">Asger Teglgaard</h3>
              <p className="text-sm text-black/70">{getStatusText(onlineStatus)}</p>
            </div>
          </div>
        </div>

        {/* Navigation Items */}
        <div className="p-2">
          <DropdownMenuItem
            className="flex items-center space-x-3 p-3 hover:bg-primary/10 rounded cursor-pointer transition-colors"
            onClick={() => window.location.href = '/'}
          >
            <Home className="w-4 h-4" />
            <span>Home</span>
          </DropdownMenuItem>

          <DropdownMenuItem
            className="flex items-center space-x-3 p-3 hover:bg-primary/10 rounded cursor-pointer transition-colors"
            onClick={() => window.open('https://asger.me/about', '_blank')}
          >
            <User className="w-4 h-4" />
            <span>About Me</span>
            <ExternalLink className="w-3 h-3 ml-auto opacity-50" />
          </DropdownMenuItem>

          <DropdownMenuItem
            className="flex items-center space-x-3 p-3 hover:bg-primary/10 rounded cursor-pointer transition-colors"
            onClick={() => window.open('https://asger.me/services', '_blank')}
          >
            <Briefcase className="w-4 h-4" />
            <span>Services</span>
            <ExternalLink className="w-3 h-3 ml-auto opacity-50" />
          </DropdownMenuItem>

          <DropdownMenuItem
            className="flex items-center space-x-3 p-3 hover:bg-accent/10 rounded cursor-pointer transition-colors group"
            onClick={() => window.location.href = '/booking'}
          >
            <Calendar className="w-4 h-4 group-hover:text-accent transition-colors" />
            <span className="group-hover:text-accent transition-colors">Book Consultation</span>
            <div className="ml-auto px-2 py-1 bg-accent text-white text-xs rounded-full animate-bounce-light">
              New
            </div>
          </DropdownMenuItem>

          <DropdownMenuItem
            className="flex items-center space-x-3 p-3 hover:bg-primary/10 rounded cursor-pointer transition-colors"
            onClick={() => window.location.href = '/#testimonials'}
          >
            <MessageCircle className="w-4 h-4" />
            <span>Testimonials</span>
          </DropdownMenuItem>

          <DropdownMenuItem
            className="flex items-center space-x-3 p-3 hover:bg-primary/10 rounded cursor-pointer transition-colors"
            onClick={() => window.open('mailto:<EMAIL>')}
          >
            <Mail className="w-4 h-4" />
            <span>Contact</span>
            <ExternalLink className="w-3 h-3 ml-auto opacity-50" />
          </DropdownMenuItem>
        </div>

        <DropdownMenuSeparator className="border-black" />

        {/* Footer Actions */}
        <div className="p-2">
          <DropdownMenuItem className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded cursor-pointer">
            <Settings className="w-4 h-4" />
            <span>Settings</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem className="flex items-center space-x-3 p-3 hover:bg-red-50 text-red-600 rounded cursor-pointer">
            <LogOut className="w-4 h-4" />
            <span>Sign Out</span>
          </DropdownMenuItem>
        </div>

        {/* Status Footer */}
        <div className="p-3 bg-gray-50 border-t-2 border-black">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div
                className={`w-2 h-2 rounded-full ${getStatusColor(onlineStatus)}`}
                style={getStatusStyle(onlineStatus)}
              />
              <span className="text-xs text-gray-600">
                {onlineStatus === 'online' ? 'Available now' : 
                 onlineStatus === 'away' ? 'Back soon' : 'Offline'}
              </span>
            </div>
            <span className="text-xs text-gray-500">
              {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </span>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
    </div>
  );
};

export default InteractiveLogo;